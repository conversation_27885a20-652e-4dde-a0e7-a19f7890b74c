"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

// Signup component (for account creation/registration)
const Signup = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  // State to manage focus for each input field for floating labels
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isUsernameFocused, setIsUsernameFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);
  const [isConfirmPasswordFocused, setIsConfirmPasswordFocused] = useState(false);

  // Handle registration form submission (simulated)
  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    console.log('Registration attempted with:', { email, username, password, confirmPassword });

    if (!email || !username || !password || !confirmPassword) {
      setError("Please fill in all fields.");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters long.");
      return;
    }

    console.log("Simulating registration success. Redirecting to home page.");
    // Redirect to home page after successful registration
    router.push('/');
  };

  // Handle Google Sign-Up (simulated)
  const handleGoogleSignUp = () => {
    setError('');
    console.log('Google Sign-Up attempted (simulated). Redirecting to home page.');
    // Redirect to home page after simulated Google sign-up
    router.push('/');
  };

  // Function to determine if a label should float
  const shouldLabelFloat = (isFocused, value) => isFocused || value.length > 0;

  // --- LUXURY DESIGN STYLES ---

  // Color Palette - Updated for white background and gold accents
  const colors = {
    backgroundPrimary: '#F8F8F8', // Soft off-white for the main background
    backgroundSecondary: '#FFFFFF', // Pure white for panels/forms
    textPrimary: '#333333', // Dark grey for main text
    textSecondary: '#888888', // Medium grey for subdued text
    accentGold: '#DAA520', // Elegant, slightly vibrant gold
    border: '#E0E0E0', // Very light grey for subtle borders
    errorBackground: '#FFF0F0', // Very light red for error background
    errorText: '#D32F2F', // Deeper red for error text
  };

  // Base styles for the input fields (now with underline focus)
  const inputBaseStyle = {
    padding: "20px 0 10px 0", // Adjust padding for underline look
    fontSize: "1em",
    borderRadius: "0", // No border radius for underline
    border: 'none', // No full border
    borderBottom: `1px solid ${colors.border}`, // Thin bottom border
    backgroundColor: 'transparent', // Transparent background
    color: colors.textPrimary,
    outline: "none",
    boxShadow: `none`, // No box shadow initially
    transition: "border-color 0.3s ease, border-bottom-width 0.3s ease", // Smoother transition for underline
    width: "100%",
    boxSizing: "border-box",
  };

  // Styles for input field when focused (gold underline)
  const inputFocusStyle = {
    borderColor: colors.accentGold,
    borderBottomWidth: '2px', // Thicker underline on focus
    boxShadow: `none`, // No box shadow for focused inputs
  };

  // Base styles for the label (when inside the input field)
  const labelBaseStyle = {
    position: "absolute",
    left: "0", // Align with new input padding
    top: "15px", // Aligns with the initial placeholder position
    color: colors.textSecondary, // Subtle grey, like a placeholder
    fontSize: "1em",
    pointerEvents: "none",
    transition: "top 0.3s ease, font-size 0.3s ease, color 0.3s ease, background-color 0.3s ease", // Smooth transition for floating effect
    zIndex: 1, // Ensure label is above input when floating
  };

  // Styles for the label when it floats above the input field
  const labelFloatStyle = {
    top: "-10px", // Move above the input
    fontSize: "0.8em", // Smaller font size
    color: colors.accentGold, // Gold color when active/floating
    backgroundColor: colors.backgroundSecondary, // Background to cover the border, matching form background
    padding: "0 5px", // Padding around the text when floating
    left: "-5px", // Adjust left position slightly when floating
  };

  // Button Styles - Primary (Gold Outline & Text, fills on hover)
  const buttonBaseStyle = {
    padding: '16px 32px',
    fontSize: '1.15em',
    fontWeight: '600',
    cursor: 'pointer',
    border: `1px solid ${colors.accentGold}`, // Gold outline
    borderRadius: '10px',
    background: 'transparent', // Transparent background
    color: colors.accentGold, // Gold text
    marginTop: '15px',
    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)', // Soft shadow
    transition: 'background 0.3s ease, color 0.3s ease, transform 0.2s ease-out, box-shadow 0.3s ease-out',
    width: '100%',
  };

  // Hover styles for the primary button
  const buttonHoverStyle = {
    background: colors.accentGold, // Fills with gold on hover
    color: colors.backgroundSecondary, // White text on gold
    transform: 'translateY(-2px) scale(1.01)',
    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.2)', // Enhanced shadow
  };

  // Click (active) styles for the primary button
  const buttonActiveStyle = {
    transform: 'translateY(1px) scale(0.99)',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  };

  // Google Button Styles (Blue Outline & Text, fills on hover)
  const googleButtonBaseStyle = {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '0.75rem',
    padding: '16px 1.5rem',
    fontSize: '1.1em',
    fontWeight: 'bold',
    cursor: 'pointer',
    borderRadius: '10px',
    background: 'transparent',
    border: '1px solid #4285F4', // Google blue outline
    color: '#4285F4', // Google blue text
    boxShadow: '0 4px 10px rgba(0, 0, 0, 0.08)',
    transition: 'background 0.3s ease, color 0.3s ease, transform 0.2s ease',
  };

  // Google Button Hover/Active
  const googleButtonHoverStyle = {
    background: '#4285F4',
    color: colors.backgroundSecondary,
    transform: 'translateY(-1px) scale(1.005)',
    boxShadow: '0 6px 15px rgba(0, 0, 0, 0.15)',
  };

  const googleButtonActiveStyle = {
    transform: 'translateY(0.5px) scale(0.995)',
    boxShadow: '0 3px 8px rgba(0, 0, 0, 0.1)',
  };

  return (
    <>
      <style>
        {`
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;700&display=swap');
          body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
          @media (max-width: 768px) {
            .full-page-container {
              flex-direction: column;
            }
            .left-panel {
              min-height: 250px !important; // Ensure it's visible on mobile
              width: 100%;
              flex: none !important;
              border-radius: 0; // Remove specific border radius for mobile
              box-shadow: none; // Remove specific box shadow for mobile
            }
            .right-panel {
              padding: 1.5rem !important;
            }
            .form-container {
              padding: 30px !important;
            }
            .heading {
              font-size: 2em !important;
              margin-bottom: 20px !important;
            }
          }
          @media (min-width: 640px) {
            .form-container {
              max-width: 28rem;
            }
          }
          @media (min-width: 768px) {
            .form-container {
              max-width: 32rem;
              padding: 2.5rem;
            }
            .heading {
              font-size: 3.2em; // Slightly adjusted for desktop
            }
          }
        `}
      </style>

      <div className="full-page-container" style={{
        minHeight: '100vh', // Keep full height
        paddingTop: '96px', // Add padding to push content below fixed navbar
        backgroundColor: colors.backgroundPrimary,
        display: 'flex',
        fontFamily: "'Inter', sans-serif",
        color: colors.textPrimary,
      }}>
        {/* Left side - Visual Brand Element Panel */}
        <div className="left-panel" style={{
          flex: '1.2',
          backgroundColor: colors.backgroundSecondary, // Pure white
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          minHeight: 'calc(100vh - 96px)', // Adjust for navbar height
          overflow: 'hidden',
          borderRadius: '0 15px 15px 0', // Slight border radius for visual interest
          boxShadow: '5px 0 20px rgba(0,0,0,0.05)', // Subtle shadow
          borderRight: `1px solid ${colors.border}`, // Subtle border
        }}>
          {/* Subtle gold line pattern as a background element */}
          <div style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            background: `repeating-linear-gradient(
              45deg,
              ${colors.accentGold}08, // Very subtle gold line
              ${colors.accentGold}08 10px,
              transparent 10px,
              transparent 20px
            )`,
            opacity: 0.8,
            zIndex: 0,
          }}></div>

          <div style={{
            width: '70%',
            height: '70%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            gap: '25px',
            zIndex: 1, // Ensure content is above pattern
          }}>
            {/* Elegant Logo/Icon - Camera icon in gold */}
            <svg
              width="80"
              height="80"
              viewBox="0 0 24 24"
              fill="none"
              stroke={colors.accentGold}
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
              style={{ filter: 'drop-shadow(0 0 8px rgba(218, 165, 32, 0.3))' }}
            >
              <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
              <circle cx="12" cy="13" r="3"></circle>
            </svg>
            <p style={{
              fontFamily: "'Playfair Display', serif", // Serif font for elegance
              color: colors.textPrimary,
              fontSize: '1.8em', // Larger slogan
              textAlign: 'center',
              margin: 0,
              fontWeight: '400', // Regular weight for serif
              letterSpacing: '1px',
              lineHeight: '1.3'
            }}>
              Your Vision, <br /> Our Canvas
            </p>
          </div>
        </div>

        {/* Right side - Form */}
        <div className="right-panel" style={{
          flex: '1',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '3rem',
          backgroundColor: colors.backgroundPrimary, // Soft off-white
        }}>
          <h1
            className="heading"
            style={{
              fontFamily: "'Playfair Display', serif", // Serif font for heading
              fontSize: '3.5em',
              marginBottom: '40px',
              color: colors.textPrimary,
              fontWeight: '700',
              letterSpacing: '-0.5px',
              textAlign: 'center',
            }}
          >
            Join Our Community
          </h1>
          <form
            onSubmit={handleSubmit}
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '35px', // Increased gap for airy feel
              padding: '50px',
              maxWidth: '500px',
              width: '100%',
              backgroundColor: colors.backgroundSecondary, // Pure white form background
              borderRadius: '15px',
              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.08)', // Softer shadow
              border: `1px solid ${colors.border}`, // Light border
            }}
            className="form-container"
          >
            {/* Error message display */}
            {error && (
              <div style={{ backgroundColor: colors.errorBackground, color: colors.errorText, padding: '0.75rem', borderRadius: '0.375rem', fontSize: '0.875rem', textAlign: 'center', border: `1px solid ${colors.errorText}20` }}>
                {error}
              </div>
            )}

            {/* Email Input Field Group */}
            <div style={{ position: 'relative', width: '100%' }}>
              <label
                htmlFor="email"
                style={{
                  ...labelBaseStyle,
                  ...(shouldLabelFloat(isEmailFocused, email) ? labelFloatStyle : {}),
                }}
              >
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onFocus={() => setIsEmailFocused(true)}
                onBlur={() => setIsEmailFocused(false)}
                style={{
                  ...inputBaseStyle,
                  ...(isEmailFocused ? inputFocusStyle : {}),
                }}
              />
            </div>

            {/* Username Input Field Group */}
            <div style={{ position: 'relative', width: '100%' }}>
              <label
                htmlFor="username"
                style={{
                  ...labelBaseStyle,
                  ...(shouldLabelFloat(isUsernameFocused, username) ? labelFloatStyle : {}),
                }}
              >
                Username
              </label>
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onFocus={() => setIsUsernameFocused(true)}
                onBlur={() => setIsUsernameFocused(false)}
                style={{
                  ...inputBaseStyle,
                  ...(isUsernameFocused ? inputFocusStyle : {}),
                }}
              />
            </div>

            {/* Password Input Field Group */}
            <div style={{ position: 'relative', width: '100%' }}>
              <label
                htmlFor="password"
                style={{
                  ...labelBaseStyle,
                  ...(shouldLabelFloat(isPasswordFocused, password) ? labelFloatStyle : {}),
                }}
              >
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onFocus={() => setIsPasswordFocused(true)}
                onBlur={() => setIsPasswordFocused(false)}
                style={{
                  ...inputBaseStyle,
                  ...(isPasswordFocused ? inputFocusStyle : {}),
                }}
              />
            </div>

            {/* Confirm Password Input Field Group */}
            <div style={{ position: 'relative', width: '100%' }}>
              <label
                htmlFor="confirmPassword"
                style={{
                  ...labelBaseStyle,
                  ...(shouldLabelFloat(isConfirmPasswordFocused, confirmPassword) ? labelFloatStyle : {}),
                }}
              >
                Confirm Password
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                onFocus={() => setIsConfirmPasswordFocused(true)}
                onBlur={() => setIsConfirmPasswordFocused(false)}
                style={{
                  ...inputBaseStyle,
                  ...(isConfirmPasswordFocused ? inputFocusStyle : {}),
                }}
              />
            </div>

            {/* Create Account Button */}
            <button
              type="submit"
              style={buttonBaseStyle}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
              onMouseLeave={(e) => Object.assign(e.currentTarget.style, buttonBaseStyle)}
              onMouseDown={(e) => Object.assign(e.currentTarget.style, buttonActiveStyle)}
              onMouseUp={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)}
            >
              Create Account
            </button>

            {/* Or Divider */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                margin: '1.5rem 0',
              }}
            >
              <div style={{ flexGrow: 1, borderTop: `1px solid ${colors.border}` }}></div>
              <span style={{ flexShrink: 0, margin: '0 1rem', color: colors.textSecondary, fontWeight: '500' }}>OR</span>
              <div style={{ flexGrow: 1, borderTop: `1px solid ${colors.border}` }}></div>
            </div>

            {/* Google OAuth Button */}
            <button
              type="button"
              onClick={handleGoogleSignUp}
              style={googleButtonBaseStyle}
              onMouseEnter={(e) => Object.assign(e.currentTarget.style, googleButtonHoverStyle)}
              onMouseLeave={(e) => Object.assign(e.currentTarget.style, googleButtonBaseStyle)}
              onMouseDown={(e) => Object.assign(e.currentTarget.style, googleButtonActiveStyle)}
              onMouseUp={(e) => Object.assign(e.currentTarget.style, googleButtonHoverStyle)}
            >
              {/* Google Icon - Inline SVG */}
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12.24 10.285V11.2h6.88a8.312 8.312 0 0 1-2.923 5.485 8.312 8.312 0 0 1-5.957 2.052c-4.62 0-8.384-3.764-8.384-8.384S7.62 3.018 12.24 3.018c2.42 0 4.54 1 6.13 2.585l-1.92 1.92c-1.07-1.07-2.48-1.74-4.21-1.74-3.52 0-6.38 2.86-6.38 6.38s2.86 6.38 6.38 6.38c2.81 0 4.7-1.19 5.86-2.31l1.86 1.86C17.06 20.35 14.77 21 12.24 21c-5.83 0-10.5-4.67-10.5-10.5S6.41 0 12.24 0c3.67 0 6.96 1.34 9.17 3.51L20.49 6.22C18.66 4.39 15.69 3.01 12.24 3.01z" fillRule="evenodd" clipRule="evenodd"/>
              </svg>
              Sign up with Google
            </button>

            {/* Link to Sign In */}
            <div
              style={{
                marginTop: '30px',
                fontSize: '0.98em',
                color: colors.textSecondary,
                textAlign: 'center',
              }}
            >
              Already have an account?{" "}
              <button
                type="button"
                onClick={() => router.push('/auth/signin')}
                style={{
                  color: colors.accentGold,
                  textDecoration: 'none',
                  fontWeight: '600',
                  transition: 'color 0.2s ease',
                  background: 'none',
                  border: 'none',
                  padding: 0,
                  cursor: 'pointer',
                }}
                onMouseEnter={(e) => (e.currentTarget.style.color = '#C59A30')} // Lighter gold on hover
                onMouseLeave={(e) => (e.currentTarget.style.color = colors.accentGold)}
              >
                Sign in here
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default Signup;
