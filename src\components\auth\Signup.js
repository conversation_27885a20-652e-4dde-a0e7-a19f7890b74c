"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

// Signup component (for account creation/registration)
const Signup = () => {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  // State to manage focus for each input field for floating labels
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isUsernameFocused, setIsUsernameFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);
  const [isConfirmPasswordFocused, setIsConfirmPasswordFocused] = useState(false);

  // Handle registration form submission (simulated)
  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    console.log('Registration attempted with:', { email, username, password, confirmPassword });
    
    if (!email || !username || !password || !confirmPassword) {
      setError("Please fill in all fields.");
      return;
    }
    
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    
    if (password.length < 6) {
      setError("Password must be at least 6 characters long.");
      return;
    }
    
    console.log("Simulating registration success. Redirecting to home page.");
    // Redirect to home page after successful registration
    router.push('/');
  };

  // Handle Google Sign-Up (simulated)
  const handleGoogleSignUp = () => {
    setError('');
    console.log('Google Sign-Up attempted (simulated). Redirecting to home page.');
    // Redirect to home page after simulated Google sign-up
    router.push('/');
  };

  // Function to determine if a label should float
  const shouldLabelFloat = (isFocused, value) => isFocused || value.length > 0;

  // Base styles for the input fields
  const inputBaseStyle = {
    padding: "20px 15px 10px 15px",
    fontSize: "1em",
    borderRadius: "8px",
    border: "1px solid #30363d", // Default border color
    backgroundColor: "#0d1117",
    color: "#f0f6fc",
    outline: "none",
    boxShadow: "inset 0 1px 3px rgba(0, 0, 0, 0.2)",
    transition: "border-color 0.2s ease, box-shadow 0.2s ease", // Transition for border/shadow on focus
    width: "100%", // Ensure inputs take full width of form
    boxSizing: "border-box",
  };

  // Styles for input field when focused
  const inputFocusStyle = {
    borderColor: "#58a6ff",
    boxShadow: "inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 3px rgba(88, 166, 255, 0.4)", // Blue glow shadow
  };

  // Base styles for the label (when inside the input field)
  const labelBaseStyle = {
    position: "absolute",
    left: "15px",
    top: "15px", // Aligns with the initial placeholder position
    color: "#8b949e", // Lighter gray, like a placeholder
    fontSize: "1em",
    pointerEvents: "none", // Allows clicks to pass through to the input
    transition: "top 0.2s ease, font-size 0.2s ease, color 0.2s ease, background-color 0.2s ease", // Smooth transition for floating effect
    zIndex: 1, // Ensure label is above input when floating
  };

  // Styles for the label when it floats above the input field
  const labelFloatStyle = {
    top: "-10px", // Move above the input
    fontSize: "0.8em", // Smaller font size
    color: "#58a6ff", // Blue color when active/floating
    backgroundColor: "#161b22", // Background to cover the border, matching form background
    padding: "0 5px", // Padding around the text when floating
    left: "10px", // Adjust left position slightly when floating
  };

  // Button Styles
  const buttonBaseStyle = {
    padding: '15px 30px',
    fontSize: '1.1em',
    fontWeight: 'bold',
    cursor: 'pointer',
    border: 'none',
    borderRadius: '8px',
    backgroundColor: '#2ea44f',
    color: '#ffffff',
    marginTop: '10px',
    transition: 'background-color 0.2s ease, transform 0.2s ease-out, box-shadow 0.2s ease-out', // Added box-shadow to transition
    boxShadow: '0 4px 15px rgba(46, 164, 79, 0.3)',
  };

  // Hover styles for the button
  const buttonHoverStyle = {
    backgroundColor: '#2c974b', // Darker green on hover
    transform: 'scale(1.02)', // Slight pulse/grow effect
    boxShadow: '0 6px 20px rgba(46, 164, 79, 0.4)', // Enhanced shadow on hover
  };

  // Click (active) styles for the button
  const buttonActiveStyle = {
    transform: 'scale(0.98)', // Press down effect
    boxShadow: '0 2px 8px rgba(46, 164, 79, 0.2)', // Smaller shadow on press
  };

  return (
    <>
      <style>
        {`
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
          body {
            font-family: 'Inter', sans-serif;
          }
          @media (min-width: 640px) {
            .form-container {
              max-width: 28rem;
            }
          }
          @media (min-width: 768px) {
            .form-container {
              max-width: 32rem;
              padding: 2.5rem;
            }
            .heading {
              font-size: 3.75rem;
            }
          }
        `}
      </style>

      <div style={{
        minHeight: '100vh',
        backgroundColor: '#0d1117',
        display: 'flex',
        fontFamily: "'Inter', sans-serif",
        color: '#c9d1d9'
      }}>
        {/* Left side - Photo placeholder */}
        <div style={{
          flex: '1',
          backgroundColor: '#161b22',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          minHeight: '100vh'
        }}>
          <div style={{
            width: '80%',
            height: '80%',
            backgroundColor: '#21262d',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '2px dashed #30363d',
            flexDirection: 'column',
            gap: '20px'
          }}>
            <div style={{
              width: '120px',
              height: '120px',
              backgroundColor: '#30363d',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '48px',
              color: '#8b949e'
            }}>
              📸
            </div>
            <p style={{
              color: '#8b949e',
              fontSize: '1.2em',
              textAlign: 'center',
              margin: 0
            }}>
              Photo Placeholder
            </p>
          </div>
        </div>

        {/* Right side - Form */}
        <div style={{
          flex: '1',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '2rem',
          backgroundColor: '#0d1117'
        }}>
          <h1
            style={{
              fontSize: '2.8em',
              marginBottom: '30px',
              color: '#ffffff',
              fontWeight: '600',
              letterSpacing: '-0.5px',
              textAlign: 'center',
            }}
          >
            Create Your Account
          </h1>
      <form
        onSubmit={handleSubmit}
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '25px',
          padding: '40px',
          maxWidth: '450px',
          width: '100%',
          backgroundColor: '#161b22',
          borderRadius: '12px',
          boxShadow: '0 8px 30px rgba(0, 0, 0, 0.4)',
          border: '1px solid #30363d',
        }}
        className="form-container"
      >
        {/* Error message display */}
        {error && (
          <div style={{ backgroundColor: '#7f1d1d', color: '#fca5a5', padding: '0.75rem', borderRadius: '0.375rem', fontSize: '0.875rem', textAlign: 'center' }}>
            {error}
          </div>
        )}

        {/* Email Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="email"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isEmailFocused, email) ? labelFloatStyle : {}),
            }}
          >
            Email Address
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            onFocus={() => setIsEmailFocused(true)}
            onBlur={() => setIsEmailFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isEmailFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Username Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="username"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isUsernameFocused, username) ? labelFloatStyle : {}),
            }}
          >
            Username
          </label>
          <input
            id="username"
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            onFocus={() => setIsUsernameFocused(true)}
            onBlur={() => setIsUsernameFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isUsernameFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Password Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="password"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isPasswordFocused, password) ? labelFloatStyle : {}),
            }}
          >
            Password
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onFocus={() => setIsPasswordFocused(true)}
            onBlur={() => setIsPasswordFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isPasswordFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Confirm Password Input Field Group */}
        <div style={{ position: 'relative', width: '100%' }}>
          <label
            htmlFor="confirmPassword"
            style={{
              ...labelBaseStyle,
              ...(shouldLabelFloat(isConfirmPasswordFocused, confirmPassword) ? labelFloatStyle : {}),
            }}
          >
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            onFocus={() => setIsConfirmPasswordFocused(true)}
            onBlur={() => setIsConfirmPasswordFocused(false)}
            style={{
              ...inputBaseStyle,
              ...(isConfirmPasswordFocused ? inputFocusStyle : {}),
            }}
          />
        </div>

        {/* Create Account Button */}
        <button
          type="submit"
          style={buttonBaseStyle} // Apply base style
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)} // Apply hover styles
          onMouseLeave={(e) => Object.assign(e.currentTarget.style, buttonBaseStyle)} // Revert to base styles
          onMouseDown={(e) => Object.assign(e.currentTarget.style, buttonActiveStyle)} // Apply active styles
          onMouseUp={(e) => Object.assign(e.currentTarget.style, buttonHoverStyle)} // Revert to hover styles on mouse up (if still hovered)
        >
          Create Account
        </button>

        {/* Or Divider */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            margin: '1rem 0',
          }}
        >
          <div style={{ flexGrow: 1, borderTop: '1px solid #4a5568' }}></div>
          <span style={{ flexShrink: 0, margin: '0 1rem', color: '#a0aec0' }}>OR</span>
          <div style={{ flexGrow: 1, borderTop: '1px solid #4a5568' }}></div>
        </div>

        {/* Google OAuth Button */}
        <button
          type="button"
          onClick={handleGoogleSignUp}
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.75rem',
            padding: '1rem 1.5rem',
            fontSize: '1.125rem',
            fontWeight: 'bold',
            cursor: 'pointer',
            borderRadius: '0.5rem',
            backgroundColor: '#4285f4',
            color: '#ffffff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            transition: 'background-color 0.2s ease, transform 0.2s ease',
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#3367d6'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4285f4'}
          onMouseDown={(e) => e.currentTarget.style.transform = 'scale(0.98)'}
          onMouseUp={(e) => e.currentTarget.style.transform = 'scale(1)'}
        >
          Sign up with Google
        </button>

        {/* Link to Sign In */}
        <div
          style={{
            marginTop: '20px',
            fontSize: '0.95em',
            color: '#8b949e',
            textAlign: 'center',
          }}
        >
          Already have an account?{" "}
          <button
            type="button"
            onClick={() => router.push('/auth/signin')}
            style={{
              color: '#58a6ff',
              textDecoration: 'none',
              fontWeight: 'bold',
              transition: 'color 0.2s ease',
              background: 'none',
              border: 'none',
              padding: 0,
              cursor: 'pointer',
            }}
            onMouseEnter={(e) => (e.currentTarget.style.color = '#79c0ff')}
            onMouseLeave={(e) => (e.currentTarget.style.color = '#58a6ff')}
          >
            Sign in here
          </button>
        </div>
      </form>
        </div>
      </div>
    </>
  );
};

export default Signup;
